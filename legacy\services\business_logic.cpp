#include "business_logic.h"
#include "../utils/simple_logger.h"
#include <QTimer>

// ==================== LoginBusinessLogic 实现 ====================

LoginBusinessLogic::LoginBusinessLogic(QObject* parent)
    : QObject(parent)
    , m_requestBuilder(createDefaultRequestBuilder())
    , m_responseProcessor(createDefaultResponseProcessor())
    , m_retryTimer(new QTimer(this))
{
    m_retryTimer->setSingleShot(true);
    connect(m_retryTimer, &QTimer::timeout, this, &LoginBusinessLogic::handleRetryTimer);
}

void LoginBusinessLogic::executeLogin(const QString& username,
                                     const QString& password,
                                     const LoginConfig& config)
{
    LOG_INFO(LogCategory::LOGIN, QString("开始执行登录流程: %1").arg(username));

    // 验证凭据
    if (!validateCredentials(username, password)) {
        emit loginFailed("用户名或密码格式无效", 0);
        return;
    }

    // 保存配置和凭据（现在构造函数已经设置了默认值）
    m_currentConfig = config;
    m_pendingUsername = username;
    m_pendingPassword = password;
    m_currentRetryCount = 0;
    
    emit loginStarted(username);
    emit loginProgress("加密密码", 10);
    
    // 加密密码
    QString encryptedPassword = m_encryptionService->encryptPassword(password);
    if (encryptedPassword.isEmpty()) {
        emit loginFailed("密码加密失败", 0);
        return;
    }
    
    // 开始预检验
    emit loginProgress("预检验", 30);
    executePreCheck(username, encryptedPassword);
}

bool LoginBusinessLogic::validateCredentials(const QString& username, const QString& password) const
{
    return !username.trimmed().isEmpty() && 
           !password.isEmpty() && 
           username.length() >= 3 && 
           password.length() >= 6;
}

void LoginBusinessLogic::logout()
{
    LOG_INFO(LogCategory::LOGIN, "执行登出操作");
    
    m_currentToken.clear();
    m_currentUser = ResponseProcessor::LoginResult();
    
    // 停止重试定时器
    if (m_retryTimer->isActive()) {
        m_retryTimer->stop();
    }
    
    LOG_INFO(LogCategory::LOGIN, "登出完成");
}

void LoginBusinessLogic::executePreCheck(const QString& username, const QString& encryptedPassword)
{
    auto request = m_requestBuilder.buildPreCheckRequest(username, encryptedPassword);
    
    // NetworkClient已删除，需要使用其他网络实现
    QString result = "NetworkClient已删除";
    
    if (!result.isSuccess()) {
        handleLoginRetry("预检验网络请求失败: " + result.errorMessage);
        return;
    }
    
    auto preCheckResult = m_responseProcessor.processPreCheckResponse(result.data);
    if (!preCheckResult.isSuccess()) {
        handleLoginRetry("预检验失败: " + preCheckResult.message);
        return;
    }
    
    // 提取LoginID
    QString loginId = preCheckResult.data["LoginID"].toString();
    if (loginId.isEmpty()) {
        handleLoginRetry("无法获取LoginID");
        return;
    }
    
    // 执行登录步骤
    emit loginProgress("执行登录", 70);
    executeLoginStep(username, encryptedPassword, loginId);
}

void LoginBusinessLogic::executeLoginStep(const QString& username, const QString& encryptedPassword, const QString& loginId)
{
    auto request = m_requestBuilder.buildLoginRequest(username, encryptedPassword, loginId);
    
    // NetworkClient已删除，需要使用其他网络实现
    QString result = "NetworkClient已删除";
    
    if (!result.isSuccess()) {
        handleLoginRetry("登录网络请求失败: " + result.errorMessage);
        return;
    }
    
    auto loginResult = m_responseProcessor.processLoginResponse(result.data);
    if (!loginResult.isValid()) {
        handleLoginRetry("登录失败: " + loginResult.message);
        return;
    }
    
    // 登录成功
    emit loginProgress("登录成功", 100);
    updateLoginState(loginResult);
    emit loginCompleted(true, "登录成功");
}

void LoginBusinessLogic::handleLoginRetry(const QString& error)
{
    m_currentRetryCount++;
    
    if (m_currentConfig.autoRetry && m_currentRetryCount <= m_currentConfig.maxRetries) {
        LOG_WARNING(LogCategory::LOGIN, QString("登录失败，准备重试 (%1/%2): %3")
                   .arg(m_currentRetryCount).arg(m_currentConfig.maxRetries).arg(error));
        
        emit loginFailed(error, m_currentRetryCount);
        
        // 启动重试定时器
        m_retryTimer->start(m_currentConfig.retryDelayMs);
    } else {
        LOG_ERROR(LogCategory::LOGIN, QString("登录最终失败: %1").arg(error));
        emit loginCompleted(false, error);
    }
}

void LoginBusinessLogic::updateLoginState(const ResponseProcessor::LoginResult& result)
{
    m_currentToken = result.token;
    m_currentUser = result;
    
    LOG_INFO(LogCategory::LOGIN, QString("登录状态已更新，用户: %1").arg(result.nickname));
}

void LoginBusinessLogic::handleRetryTimer()
{
    LOG_INFO(LogCategory::LOGIN, QString("开始第%1次重试").arg(m_currentRetryCount));
    
    // 重新开始登录流程
    QString encryptedPassword = m_encryptionService->encryptPassword(m_pendingPassword);
    executePreCheck(m_pendingUsername, encryptedPassword);
}

// ==================== OrderBusinessLogic 实现 ====================

OrderBusinessLogic::OrderBusinessLogic(QObject* parent)
    : QObject(parent)
    , m_requestBuilder(createDefaultRequestBuilder())
    , m_responseProcessor(createDefaultResponseProcessor())
    , m_autoRefreshTimer(new QTimer(this))
{
    connect(m_autoRefreshTimer, &QTimer::timeout, this, &OrderBusinessLogic::handleAutoRefreshTimer);
}

void OrderBusinessLogic::refreshOrders(const QString& token,
                                      const QString& userId,
                                      const OrderConfig& config)
{
    LOG_DEBUG(LogCategory::ORDER, "开始刷新订单列表");
    emit ordersRefreshStarted();

    auto request = m_requestBuilder.buildOrderListRequest(token, userId, config.gameId);
    
    // NetworkClient已删除，需要使用其他网络实现
    QString result = "NetworkClient已删除";
    
    if (!result.isSuccess()) {
        LOG_ERROR(LogCategory::ORDER, "订单列表请求失败: " + result.errorMessage);
        return;
    }
    
    handleOrderListResponse(result.data);
}

void OrderBusinessLogic::acceptOrder(const QString& orderId,
                                    const QString& token,
                                    const QString& userId,
                                    const QString& payPassword,
                                    const QString& loginId)
{
    LOG_INFO(LogCategory::ORDER, QString("开始接单: %1").arg(orderId));
    emit orderAcceptStarted(orderId);
    
    auto request = m_requestBuilder.buildAcceptOrderRequest(orderId, token, userId, payPassword, loginId);
    
    // NetworkClient已删除，需要使用其他网络实现
    QString result = "NetworkClient已删除";
    
    if (!result.isSuccess()) {
        LOG_ERROR(LogCategory::ORDER, "接单请求失败: " + result.errorMessage);
        emit orderAccepted(orderId, false, result.errorMessage);
        return;
    }
    
    handleAcceptOrderResponse(orderId, result.data);
}

void OrderBusinessLogic::startAutoRefresh(const QString& token, const QString& userId, const OrderConfig& config)
{
    LOG_INFO(LogCategory::ORDER, QString("开始自动刷新，间隔: %1ms").arg(config.refreshIntervalMs));

    m_autoRefreshToken = token;
    m_autoRefreshUserId = userId;
    m_autoRefreshConfig = config;

    m_autoRefreshTimer->start(config.refreshIntervalMs);

    // 立即刷新一次
    refreshOrders(token, userId, config);
}

void OrderBusinessLogic::stopAutoRefresh()
{
    LOG_INFO(LogCategory::ORDER, "停止自动刷新");
    m_autoRefreshTimer->stop();
}

void OrderBusinessLogic::handleOrderListResponse(const QString& response)
{
    auto orders = m_responseProcessor.processOrderListResponse(response);
    
    // 验证订单数据
    QList<ResponseProcessor::OrderInfo> validOrders;
    for (const auto& order : orders) {
        if (validateOrderData(order)) {
            validOrders.append(order);
        }
    }
    
    m_currentOrders = validOrders;
    
    LOG_INFO(LogCategory::ORDER, QString("订单列表更新完成，共%1条有效订单").arg(validOrders.size()));
    emit ordersRefreshed(validOrders);
}

void OrderBusinessLogic::handleAcceptOrderResponse(const QString& orderId, const QString& response)
{
    auto result = m_responseProcessor.processAcceptOrderResponse(response);
    
    LOG_INFO(LogCategory::ORDER, QString("接单结果: %1 - %2").arg(result.success ? "成功" : "失败", result.message));
    emit orderAccepted(orderId, result.success, result.message);
}

bool OrderBusinessLogic::validateOrderData(const ResponseProcessor::OrderInfo& order) const
{
    return order.isValid() && 
           order.price > 0 && 
           !order.serialNo.isEmpty() && 
           !order.title.isEmpty();
}

void OrderBusinessLogic::handleAutoRefreshTimer()
{
    refreshOrders(m_autoRefreshToken, m_autoRefreshUserId, m_autoRefreshConfig);
}

// ==================== UserBusinessLogic 实现 ====================

UserBusinessLogic::UserBusinessLogic(QObject* parent)
    : QObject(parent)
    , m_requestBuilder(createDefaultRequestBuilder())
    , m_responseProcessor(createDefaultResponseProcessor())
{
}

void UserBusinessLogic::fetchUserInfo(const QString& token, const QString& userId)
{
    LOG_DEBUG(LogCategory::API, "获取用户信息");
    
    auto request = m_requestBuilder.buildUserInfoRequest(token, userId);
    
    // NetworkClient已删除，需要使用其他网络实现
    QString result = "NetworkClient已删除";
    
    if (!result.isSuccess()) {
        LOG_ERROR(LogCategory::API, "用户信息请求失败: " + result.errorMessage);
        return;
    }
    
    handleUserInfoResponse(result.data);
}

void UserBusinessLogic::updateBalance(const QString& token, const QString& userId)
{
    // 余额信息包含在用户信息中
    fetchUserInfo(token, userId);
}

void UserBusinessLogic::checkPayPassword(const QString& token, const QString& userId)
{
    // 支付密码状态包含在用户信息中
    fetchUserInfo(token, userId);
}

void UserBusinessLogic::handleUserInfoResponse(const QString& response)
{
    auto userInfo = m_responseProcessor.processUserInfoResponse(response);
    
    if (userInfo.isValid()) {
        m_currentUserInfo = userInfo;
        
        LOG_INFO(LogCategory::API, QString("用户信息更新: %1, 余额: %2").arg(userInfo.nickname).arg(userInfo.balance));
        
        emit userInfoUpdated(userInfo);
        emit balanceUpdated(userInfo.balance);
        emit payPasswordStatusChanged(userInfo.hasPayPassword);
    }
}

// ==================== BusinessLogicFactory 实现 ====================

LoginBusinessLogic* BusinessLogicFactory::createLoginLogic(QObject* parent)
{
    return new LoginBusinessLogic(parent);
}

OrderBusinessLogic* BusinessLogicFactory::createOrderLogic(QObject* parent)
{
    return new OrderBusinessLogic(parent);
}

UserBusinessLogic* BusinessLogicFactory::createUserLogic(QObject* parent)
{
    return new UserBusinessLogic(parent);
}
