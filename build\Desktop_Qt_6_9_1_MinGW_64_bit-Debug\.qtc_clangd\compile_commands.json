[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\main.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\src\\ui\\mainwindow.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/ui/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\api\\orderapi.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/api/orderapi.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\workers\\filterworker.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/workers/filterworker.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\network\\ultrafasttls.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/ultrafasttls.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\network\\ultrafasttls_debug_monitor.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/ultrafasttls_debug_monitor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\network\\request_builder.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/request_builder.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\network\\response_processor.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/response_processor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\utils\\simple_logger.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/utils/simple_logger.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\utils\\error_handler.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/utils/error_handler.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\utils\\json_parser.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/utils/json_parser.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\services\\encryption_service.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/encryption_service.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\services\\authentication_service.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/authentication_service.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\services\\service_container.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/service_container.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\legacy\\services\\business_logic.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/business_logic.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\src\\config\\app_config.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/config/app_config.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\src\\core\\utils\\logger.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/core/utils/logger.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\src\\network\\engines\\ultrafasttls_adapter.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/network/engines/ultrafasttls_adapter.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "C:\\eee\\cc\\src\\integration\\legacy_api_adapter.cpp"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/integration/legacy_api_adapter.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\ui\\mainwindow.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/ui/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\api\\orderapi.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/api/orderapi.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\api\\api_constants.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/api/api_constants.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\workers\\filterworker.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/workers/filterworker.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\network\\ultrafasttls.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/ultrafasttls.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\network\\ultrafasttls_debug_monitor.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/ultrafasttls_debug_monitor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\network\\request_builder.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/request_builder.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\network\\response_processor.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/network/response_processor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\utils\\simple_logger.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/utils/simple_logger.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\utils\\utils.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/utils/utils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\utils\\error_handler.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/utils/error_handler.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\utils\\json_parser.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/utils/json_parser.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\services\\encryption_service.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/encryption_service.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\services\\authentication_service.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/authentication_service.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\services\\service_container.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/service_container.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\legacy\\services\\business_logic.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/legacy/services/business_logic.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\config\\app_config.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/config/app_config.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\core\\utils\\logger.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/core/utils/logger.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\network\\engines\\network_engine_interface.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/network/engines/network_engine_interface.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\network\\engines\\ultrafasttls_adapter.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/network/engines/ultrafasttls_adapter.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\core\\utils\\json_helper.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/core/utils/json_helper.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\core\\utils\\string_utils.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/core/utils/string_utils.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-DQT_QML_DEBUG", "-g", "-fdiagnostics-color=always", "-Wall", "-Wextra", "-Wpedantic", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu++17", "-DMINGW_HAS_SECURE_API", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DQT_CONCURRENT_LIB", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NEEDS_QMAIN", "-DQT_NETWORK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN32_LEAN_AND_MEAN", "-DWIN64", "-D_CRT_SECURE_NO_WARNINGS", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_SECURE_SCL=0", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\OrderManager_autogen\\include", "-IC:\\Program Files\\OpenSSL-Win64\\include", "-IC:\\eee\\cc", "-IC:\\eee\\cc\\src", "-IC:\\eee\\cc\\src\\core", "-IC:\\eee\\cc\\src\\core\\utils", "-IC:\\eee\\cc\\src\\core\\models", "-IC:\\eee\\cc\\src\\core\\services", "-IC:\\eee\\cc\\src\\core\\controllers", "-IC:\\eee\\cc\\src\\network", "-IC:\\eee\\cc\\src\\network\\engines", "-IC:\\eee\\cc\\src\\integration", "-IC:\\eee\\cc\\src\\config", "-IC:\\eee\\cc\\src\\ui", "-IC:\\eee\\cc\\legacy", "-IC:\\eee\\cc\\legacy\\api", "-IC:\\eee\\cc\\legacy\\network", "-IC:\\eee\\cc\\legacy\\services", "-IC:\\eee\\cc\\legacy\\utils", "-IC:\\eee\\cc\\legacy\\workers", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtCore", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtWidgets", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtGui", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtNetwork", "-isystem", "C:\\Qt\\6.9.1\\mingw_64\\include\\QtConcurrent", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "C:\\eee\\cc\\src\\integration\\legacy_api_adapter.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/src/integration/legacy_api_adapter.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\test\\example.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/test/example.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\test\\example.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/test/example.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\test\\minigzip.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/test/minigzip.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-D_FILE_OFFSET_BITS=64", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\test\\minigzip.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/test/minigzip.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\adler32.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/adler32.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\compress.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/compress.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\crc32.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/crc32.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\deflate.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/deflate.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzclose.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzclose.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzlib.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzlib.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzread.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzread.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzwrite.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzwrite.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inflate.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inflate.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\infback.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/infback.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inftrees.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inftrees.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inffast.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inffast.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\trees.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/trees.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\uncompr.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/uncompr.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\zutil.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/zutil.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib\\zconf.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zconf.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\zlib.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/zlib.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\crc32.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/crc32.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\deflate.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/deflate.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzguts.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzguts.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inffast.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inffast.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inffixed.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inffixed.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inflate.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inflate.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inftrees.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inftrees.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\trees.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/trees.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-DZLIB_DLL", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\zutil.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/zutil.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\adler32.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/adler32.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\compress.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/compress.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\crc32.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/crc32.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\deflate.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/deflate.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzclose.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzclose.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzlib.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzlib.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzread.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzread.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzwrite.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzwrite.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inflate.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inflate.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\infback.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/infback.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inftrees.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inftrees.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inffast.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inffast.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\trees.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/trees.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\uncompr.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/uncompr.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "C:\\Libraries\\zlib131\\zlib-1.3.1\\zutil.c"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/zutil.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib\\zconf.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zconf.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\zlib.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/zlib.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\crc32.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/crc32.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\deflate.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/deflate.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\gzguts.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/gzguts.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inffast.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inffast.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inffixed.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inffixed.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inflate.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inflate.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\inftrees.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/inftrees.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\trees.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/trees.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-std=gnu17", "-DNDEBUG", "-DNOMINMAX", "-DOPENSSL_3_PLUS", "-DOPENSSL_FOUND", "-DWIN32_LEAN_AND_MEAN", "-D_CRT_SECURE_NO_WARNINGS", "-D_HAS_ITERATOR_DEBUGGING=0", "-D_LARGEFILE64_SOURCE", "-D_SECURE_SCL=0", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-IC:\\eee\\cc\\build\\Desktop_Qt_6_9_1_MinGW_64_bit-Debug\\zlib", "-IC:\\eee\\cc", "-IC:\\Libraries\\zlib131\\zlib-1.3.1", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "C:\\Libraries\\zlib131\\zlib-1.3.1\\zutil.h"], "directory": "C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/.qtc_clangd", "file": "C:/Libraries/zlib131/zlib-1.3.1/zutil.h"}]