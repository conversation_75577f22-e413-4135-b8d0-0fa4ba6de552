#include "service_container.h"
#include "encryption_service.h"
#include "../utils/json_parser.h"
#include "authentication_service.h"
#include "../utils/simple_logger.h"
#include <vector>
#include <functional>
#include <algorithm>

ServiceContainer::ServiceContainer(QObject* parent)
    : QObject(parent)
{
}

ServiceContainer::~ServiceContainer()
{
    LOG_INFO(LogCategory::SYSTEM, "服务容器销毁");
}

bool ServiceContainer::initialize()
{
    if (m_initialized) return true;

    LOG_INFO(LogCategory::SYSTEM, "开始初始化服务容器");

    // 使用函数式编程简化初始化
    std::vector<std::function<bool()>> initFunctions = {
        [this]() { return initializeNetworkClient(); },
        [this]() { return initializeEncryptionService(); },
        [this]() { return initializeJsonParser(); },
        [this]() { return initializeAuthenticationService(); }
    };

    // 所有初始化函数都必须成功
    bool success = std::all_of(initFunctions.begin(), initFunctions.end(),
                              [](const std::function<bool()>& func) { return func(); });

    if (success) {
        setupDependencies();
        connectSignals();
        m_initialized = true;
    }

    LOG_INFO(LogCategory::SYSTEM, success ? "服务容器初始化成功" : "服务容器初始化失败");
    emit initializationCompleted(success);
    return success;
}

void ServiceContainer::setCurlPath(const QString& path)
{
    if (m_networkClient) {
        m_networkClient->setCurlPath(path);
        LOG_INFO(LogCategory::SYSTEM, QString("设置curl路径: %1").arg(path));
    }
}

void ServiceContainer::setNetworkTimeout(int timeoutMs)
{
    if (m_networkClient) {
        m_networkClient->setTimeout(timeoutMs);
        LOG_INFO(LogCategory::SYSTEM, QString("设置网络超时: %1ms").arg(timeoutMs));
    }
}

void ServiceContainer::setEncryptionKey(const QString& key)
{
    if (m_encryptionService) {
        m_encryptionService->setEncryptionKey(key);
        LOG_INFO(LogCategory::SYSTEM, "设置加密密钥");
    }
}

bool ServiceContainer::initializeNetworkClient()
{
    try {
        m_networkClient = std::make_unique<NetworkClient>(this);
        LOG_INFO(LogCategory::SYSTEM, "NetworkClient初始化成功");
        emit serviceInitialized("NetworkClient");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR(LogCategory::SYSTEM, QString("NetworkClient初始化失败: %1").arg(e.what()));
        return false;
    }
}

bool ServiceContainer::initializeEncryptionService()
{
    try {
        m_encryptionService = std::make_unique<EncryptionService>(this);
        LOG_INFO(LogCategory::SYSTEM, "EncryptionService初始化成功");
        emit serviceInitialized("EncryptionService");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR(LogCategory::SYSTEM, QString("EncryptionService初始化失败: %1").arg(e.what()));
        return false;
    }
}

bool ServiceContainer::initializeJsonParser()
{
    try {
        m_jsonParser = std::make_unique<JsonParser>(this);
        LOG_INFO(LogCategory::SYSTEM, "JsonParser初始化成功");
        emit serviceInitialized("JsonParser");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR(LogCategory::SYSTEM, QString("JsonParser初始化失败: %1").arg(e.what()));
        return false;
    }
}

bool ServiceContainer::initializeAuthenticationService()
{
    try {
        m_authenticationService = std::make_unique<AuthenticationService>(this);
        LOG_INFO(LogCategory::SYSTEM, "AuthenticationService初始化成功");
        emit serviceInitialized("AuthenticationService");
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR(LogCategory::SYSTEM, QString("AuthenticationService初始化失败: %1").arg(e.what()));
        return false;
    }
}

void ServiceContainer::setupDependencies()
{
    LOG_DEBUG(LogCategory::SYSTEM, "设置服务依赖关系");
    
    // 为AuthenticationService注入依赖
    if (m_authenticationService && m_networkClient && m_encryptionService && m_jsonParser) {
        m_authenticationService->setNetworkClient(m_networkClient.get());
        m_authenticationService->setEncryptionService(m_encryptionService.get());
        m_authenticationService->setJsonParser(m_jsonParser.get());
        
        LOG_DEBUG(LogCategory::SYSTEM, "AuthenticationService依赖注入完成");
    }
}

void ServiceContainer::connectSignals()
{
    LOG_DEBUG(LogCategory::SYSTEM, "连接服务信号");
    
    // 连接网络客户端信号
    if (m_networkClient) {
        connect(m_networkClient.get(), &NetworkClient::requestStarted,
                this, [](const QString& url) {
                    LOG_DEBUG(LogCategory::NETWORK, QString("网络请求开始: %1").arg(url));
                });
        
        connect(m_networkClient.get(), &NetworkClient::requestFinished,
                this, [](const QString& url, bool success, qint64 elapsedMs) {
                    LOG_DEBUG(LogCategory::NETWORK, 
                             QString("网络请求完成: %1, 成功: %2, 耗时: %3ms")
                             .arg(url).arg(success ? "是" : "否").arg(elapsedMs));
                });
    }
    
    // 连接认证服务信号
    if (m_authenticationService) {
        connect(m_authenticationService.get(), &AuthenticationService::authenticationChanged,
                this, [](bool authenticated) {
                    LOG_INFO(LogCategory::LOGIN, 
                            QString("认证状态变更: %1").arg(authenticated ? "已认证" : "未认证"));
                });
        
        connect(m_authenticationService.get(), &AuthenticationService::loginCompleted,
                this, [](bool success, const QString& message) {
                    LOG_INFO(LogCategory::LOGIN, 
                            QString("登录完成: %1 - %2").arg(success ? "成功" : "失败", message));
                });
    }
    
    // 连接JSON解析器信号
    if (m_jsonParser) {
        connect(m_jsonParser.get(), &JsonParser::parseError,
                this, [](const QString& error) {
                    LOG_ERROR(LogCategory::API, QString("JSON解析错误: %1").arg(error));
                });
        
        connect(m_jsonParser.get(), &JsonParser::parseSuccess,
                this, [](const QString& type) {
                    LOG_DEBUG(LogCategory::API, QString("JSON解析成功: %1").arg(type));
                });
    }
}
