# ninja log v6
224835	393129	****************	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
50	5835	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
40910	258953	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
368	6414	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
84	527844	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
3416	8756	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
375360	497511	****************	zlib/example64.exe	4a92267bf6f9e4ab
4125	8759	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
793	7039	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
45	480	****************	CMakeFiles/clean.additional	baedbe210acaa455
1264	7539	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
84	527844	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2044	8148	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
375357	496963	****************	zlib/minigzip.exe	b30485a50c6b3a85
2750	8748	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
55	1910	7756902780054450	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
375364	496316	7756909943075881	zlib/minigzip64.exe	4f10a1aa1eb3582e
4700	8762	7756902668754695	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
375351	496860	7756909942955453	zlib/example.exe	61852f41d57c7507
5255	8764	7756902674324501	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
5837	8767	7756902680145710	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
6415	8769	7756902685929309	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
7040	9316	7756902692175953	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
40444	249616	7756904071920501	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
122068	340425	7756908484048614	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
7540	9671	7756902697175208	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
8148	10281	7756902703220158	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
55	1910	7756902780054450	zlib/zlib1rc.obj	5625031d43e5c7be
1911	88063	7756902780132727	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
57	249003	7756903668069920	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
81763	299853	7756908081030932	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
53	256820	7756907263897323	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
10282	11792	7756902724600218	zlib/libzlibstatic.a	22b16c9e053b901
162137	380949	7756908884757551	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
173972	391158	7756909003107608	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
88063	305032	7756903641660194	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
211067	393126	7756909374028175	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
215254	393128	7756909415909197	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
84	527844	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
527845	597854	7756911467811678	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
256821	393130	7756909831600371	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
258953	393131	7756909852921830	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
299854	410380	7756910261918793	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
528038	598749	7756911469812346	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
340425	410503	7756910667611295	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
380950	410421	7756911072888515	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
391158	411153	7756911174948198	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
393131	411736	7756911380590162	zlib/libzlib.dll	c70d7c7719fed2bf
393131	411736	7756911380590162	zlib/libzlib.dll.a	c70d7c7719fed2bf
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
84	527844	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
664680	671232	7756912836240263	OrderManager.exe	130079037154008e
15	3357	7755841096121232	build.ninja	497fbd042d0239fc
480	831	7756902464911758	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
597855	657992	7756912167976387	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
600965	657809	7756912199088570	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
601833	657910	7756912207763131	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
528407	602523	7756911473470502	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
529615	583221	7756911485595774	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
530595	596740	7756911495368214	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
530132	601832	7756911490754296	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
583221	657498	7756912021647264	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
588897	652338	7756912078366598	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
593593	657500	7756912125369038	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
531587	588897	77569***********	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
532444	597402	7756911513869489	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
528903	600965	7756911478461583	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
596740	657501	7756912156781180	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
598752	657692	7756912176960066	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
531095	593593	7756911500387727	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
597402	657503	7756912163458287	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
652339	664680	7756912712794889	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
602523	657505	7756912214671183	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
65	5003	7756918892017756	OrderManager_autogen/timestamp	21ad19f5d651830a
65	5003	7756918892017756	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
65	5003	7756918892017756	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
65	5003	7756918892017756	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
75	7869	7756918892207459	zlib/minigzip.exe	b30485a50c6b3a85
88	7990	7756918892320120	zlib/minigzip64.exe	4f10a1aa1eb3582e
71	8345	7756918892160289	zlib/example.exe	61852f41d57c7507
81	8491	7756918892261314	zlib/example64.exe	4a92267bf6f9e4ab
5006	15364	7756918941501569	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
15365	26835	7756919045097850	OrderManager.exe	130079037154008e
50	10060	7756919067701234	OrderManager.exe	130079037154008e
64	2302	7756926220222813	OrderManager_autogen/timestamp	21ad19f5d651830a
64	2302	7756926220222813	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
64	2302	7756926220222813	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
64	2302	7756926220222813	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2305	13499	7756926242626079	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
37	8107	7756929018048020	OrderManager.exe	130079037154008e
