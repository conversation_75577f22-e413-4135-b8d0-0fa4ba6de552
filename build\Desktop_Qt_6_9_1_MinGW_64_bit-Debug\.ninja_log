# ninja log v6
5647	7114	7756523444432738	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
21	1751	7756523388168783	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
7997	16762	****************	zlib/example64.exe	4a92267bf6f9e4ab
1249	2611	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
5407	31111	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
1048	2610	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
136	1906	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
4204	5837	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
13	178	****************	CMakeFiles/clean.additional	baedbe210acaa455
395	2073	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
5407	31111	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
551	2256	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
7995	16875	****************	zlib/minigzip.exe	b30485a50c6b3a85
704	2426	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
7998	16314	****************	zlib/minigzip64.exe	4f10a1aa1eb3582e
2608	3534	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
865	2608	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
7993	16386	****************	zlib/example.exe	61852f41d57c7507
1401	2611	****************	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
1572	2612	7756523403655027	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1752	2612	7756523405472265	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
1906	2613	7756523407018370	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
4528	6235	7756523433148382	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
3863	5410	7756523426581851	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
2073	2802	7756523408650990	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
2256	2901	7756523410509720	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2426	3147	7756523412079347	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2608	3534	****************	zlib/zlib1rc.obj	5625031d43e5c7be
3534	5075	****************	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
4366	6029	7756523431555446	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
3711	5255	7756523424927737	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
3147	5407	7756523419432805	zlib/libzlibstatic.a	22b16c9e053b901
4037	5647	7756523428247590	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
4734	6451	7756523435196397	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
4910	6637	7756523436930804	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
5075	6869	7756523438680724	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
5256	7113	7756523440499610	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
31112	68745	7756523699057676	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
5407	31111	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
5410	7113	7756523442051381	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
5837	7115	7756523446332854	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
6029	7115	7756523448241984	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
6235	7118	7756523450283699	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
6451	7252	7756523452339103	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
31310	65275	7756523700983990	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
6637	7540	7756523454312223	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
6869	7710	7756523456613734	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
7115	7993	7756523467790662	zlib/libzlib.dll	c70d7c7719fed2bf
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
7115	7993	7756523467790662	zlib/libzlib.dll.a	c70d7c7719fed2bf
5407	31111	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
91404	97304	7756524301882079	OrderManager.exe	130079037154008e
15	3357	7755841096121232	build.ninja	497fbd042d0239fc
178	337	7756523379829898	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
63714	85007	7756524025085567	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
68746	86287	7756524075417886	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
31596	86667	7756523703895798	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
70487	86191	7756524092835694	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
32191	58991	7756523709863032	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
33027	56622	7756523718228931	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
32553	63713	7756523713467525	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
54833	70487	7756523936292881	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
56622	75754	7756523954187071	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
57455	78315	7756523962502608	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
33881	57454	7756523726762943	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
34396	58041	7756523731918435	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
31861	77818	7756523706474969	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
58042	78311	7756523968373462	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
65275	85041	7756524040709885	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
33463	54832	7756523722562498	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
58991	83385	7756523977863410	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
77819	91404	7756524166153831	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
75755	90977	7756524145501730	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
23	5855	7756536040281351	OrderManager_autogen/timestamp	21ad19f5d651830a
23	5855	7756536040281351	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
23	5855	7756536040281351	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
23	5855	7756536040281351	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
7499	28361	7756536060127841	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
7935	29583	7756536064525258	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
6059	32000	7756536045686414	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
5857	33694	7756536043609477	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
6727	35355	7756536052353951	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
6370	39028	7756536048866076	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
23	829	7756538985862258	OrderManager_autogen/timestamp	21ad19f5d651830a
23	829	7756538985862258	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
23	829	7756538985862258	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
23	829	7756538985862258	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
20	843	7756540916200869	OrderManager_autogen/timestamp	21ad19f5d651830a
20	843	7756540916200869	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
20	843	7756540916200869	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
20	843	7756540916200869	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
844	5253	7756540924415481	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
5253	9569	7756540968504435	OrderManager.exe	130079037154008e
20	786	7756546443345769	OrderManager_autogen/timestamp	21ad19f5d651830a
20	786	7756546443345769	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
20	786	7756546443345769	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
20	786	7756546443345769	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
787	5192	7756546450901693	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
5192	9580	7756546495048964	OrderManager.exe	130079037154008e
22	853	7756781445797247	OrderManager_autogen/timestamp	21ad19f5d651830a
22	853	7756781445797247	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
22	853	7756781445797247	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
22	853	7756781445797247	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
984	10751	7756781455346619	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
854	14119	7756781454078960	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
14119	20107	7756781586728143	OrderManager.exe	130079037154008e
