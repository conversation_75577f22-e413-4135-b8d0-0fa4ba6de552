# ninja log v6
131066	229400	****************	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
38	5179	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
6177	147013	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
459	5705	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
58	1699	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
3026	7852	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
264144	281589	****************	zlib/example64.exe	4a92267bf6f9e4ab
3639	7853	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
901	6259	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
67	673	****************	CMakeFiles/clean.additional	baedbe210acaa455
1422	6801	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
58	1699	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1954	7309	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
264139	280916	****************	zlib/minigzip.exe	b30485a50c6b3a85
2483	7848	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
55	2153	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
264148	281457	7756962472518607	zlib/minigzip64.exe	4f10a1aa1eb3582e
4096	7854	7757042704840641	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
264134	280812	7756962472375728	zlib/example.exe	61852f41d57c7507
4700	7855	7757042710892554	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
5180	7856	7757042715678945	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
5705	7856	7757042720936788	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
6260	8211	7757042726488297	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
3233	90768	7756959863366198	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
74306	171500	7756960573947860	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
6802	8586	7757042731906185	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
7309	9043	7757042736972970	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
55	2153	****************	zlib/zlib1rc.obj	5625031d43e5c7be
2154	78347	7756959852567608	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
2694	89768	7756959857978143	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
46989	170556	7756960300929871	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
4038	131066	7756959871411825	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
9044	10193	7757042754318443	zlib/libzlibstatic.a	22b16c9e053b901
76468	211795	7756960595622789	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
77357	213789	7756960604600185	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
78348	214755	7756960614496899	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
89768	229398	7756960728722883	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
90768	229399	7756960738634663	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
58	1699	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
9690	37944	7757034211460147	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
147014	251615	7756961301174774	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
170556	252482	7756961536593840	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
171500	262298	7756961546043889	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
10050	33129	7757034214996787	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
211796	262106	7756961948995455	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
213789	262182	7756961968926997	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
214756	263304	7756961978591173	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
252482	264134	7756962472087145	zlib/libzlib.dll	c70d7c7719fed2bf
252482	264134	7756962472087145	zlib/libzlib.dll.a	c70d7c7719fed2bf
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
58	1699	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
29143	37769	7757037362222904	OrderManager.exe	d9543c243f221849
41	3743	7756956212648396	build.ninja	497fbd042d0239fc
673	1186	7757037980869130	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
4168	20015	7756992091420182	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
9069	105863	7757013683317063	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
54872	80000	7756972464402272	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
86	29142	7757037071637675	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
290077	334822	7756962731803103	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
291282	343107	7756962743855210	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
7168	98287	7757013664275882	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
293501	344929	7756962766051320	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
7872	67820	7756971994319924	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
8222	69896	7756971997887991	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
7633	101334	7757013668918976	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
292518	345362	7756962756216855	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
13768	49376	7757029948982452	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
8582	54871	7756972001507661	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
4588	20692	7756992095677779	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
165137	190565	7756943652131103	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
8192	105896	7757013674567495	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
14685	40471	7757029958125531	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
************	7757013693584957	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
27	335	7757043041426144	CMakeFiles/clean.additional	baedbe210acaa455
335	602	7757043044508358	clean	577b940aee94fbd7
67	8975	7757043904389972	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
387	9600	7757043907599164	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
883	10328	7757043912551753	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
1365	11029	7757043917383474	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
1954	11661	7757043923267755	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
2549	12245	7757043929217056	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
5370	12251	7757043957421151	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
7119	12253	7757043974915548	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
7687	12254	7757043980598984	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
8321	12256	7757043986935172	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
8977	12258	7757043993497850	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
9600	12260	7757043999732191	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
10328	12965	7757044007013185	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
11029	13309	7757044014018314	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
11661	14016	7757044020336709	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
12246	14821	7757044051864867	zlib/zlib1rc.obj	5625031d43e5c7be
12246	14821	7757044051864867	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
14822	24371	7757044051937533	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
15279	43197	7757044056511494	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
14017	61596	7757044043888801	zlib/libzlibstatic.a	22b16c9e053b901
15985	61602	7757044063567418	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
16603	101715	7757044069748455	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
17228	106069	7757044076008078	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
18079	126943	7757044084521367	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
18913	131550	7757044092862443	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
19645	138841	7757044100178808	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
20521	142219	7757044108934294	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
24372	145587	7757044147442828	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
43198	154867	7757044335698249	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
61603	182809	7757044519750829	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
101715	187496	7757044920873885	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
142219	188289	7757045325917146	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
106069	188402	7757044964411550	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
138842	188469	7757045292148613	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
126944	188674	7757045173168523	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
131551	188816	7757045219238897	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
145587	189584	7757045359594098	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
188675	190554	7757045809130470	zlib/libzlib.dll	c70d7c7719fed2bf
188675	190554	7757045809130470	zlib/libzlib.dll.a	c70d7c7719fed2bf
190557	205629	7757045809297366	zlib/minigzip.exe	b30485a50c6b3a85
190555	205716	7757045809273993	zlib/example.exe	61852f41d57c7507
190563	206178	7757045809342111	zlib/minigzip64.exe	4f10a1aa1eb3582e
190560	206916	7757045809320340	zlib/example64.exe	4a92267bf6f9e4ab
61596	216739	7757046067672149	OrderManager_autogen/timestamp	21ad19f5d651830a
61596	216739	7757046067672149	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
61596	216739	7757046067672149	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
61596	216739	7757046067672149	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
217863	269350	7757046082351212	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
219266	273758	7757046096385640	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
218822	277051	7757046091952819	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
219715	287375	7757046100874863	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
218328	288280	7757046087004550	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
216949	290255	7757046073209049	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
216740	292422	7757046071116376	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
217512	295780	7757046078845853	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
217213	304671	7757046075856549	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
220262	311664	7757046106347224	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
269350	360517	7757046597230413	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
273759	364034	7757046641317331	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
277051	375513	7757046674237544	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
287376	379105	7757046777478810	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
290255	380072	7757046806275517	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
304672	380131	7757046950437660	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
292422	380217	7757046827945260	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
295780	380244	7757046861518504	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
288280	380363	7757046786532132	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
311665	386069	7757047020370490	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
386070	398965	7757047764421830	OrderManager.exe	d9543c243f221849
70	3185	7757052687303569	OrderManager_autogen/timestamp	21ad19f5d651830a
70	3185	7757052687303569	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
70	3185	7757052687303569	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
70	3185	7757052687303569	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
3189	20545	7757052718356607	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
20546	27780	7757052891979584	OrderManager.exe	d9543c243f221849
