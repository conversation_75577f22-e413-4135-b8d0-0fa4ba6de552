#include "app_config.h"
#include <QApplication>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QFile>
#include <QDebug>

AppConfig& AppConfig::instance()
{
    static AppConfig instance;
    return instance;
}

void AppConfig::load()
{
    if (!m_settings) {
        QString configPath = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
        QDir().mkpath(configPath);
        m_settings = new QSettings(configPath + "/config.ini", QSettings::IniFormat);
    }
    
    initializeDefaults();
    loadFromSettings();
}

void AppConfig::save()
{
    if (!m_settings) {
        load(); // 确保settings已初始化
    }
    
    saveToSettings();
    m_settings->sync();
}

void AppConfig::reset()
{
    initializeDefaults();
    save();
}

void AppConfig::initializeDefaults()
{
    // 网络配置默认值
    m_networkConfig.engineType = "UltraFastTLS";
    m_networkConfig.timeout = 30000;
    m_networkConfig.refreshInterval = 1800;
    m_networkConfig.enableDebugLog = false;
    m_networkConfig.userAgent = "Mozilla/5.0 (Linux; U; Android 15; zh-CN; V2307A Build/AP3A.240905.015.A1) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.80 Quark/7.14.5.880 Mobile Safari/537.36";
    
    // UI配置默认值
    m_uiConfig.pageSize = 50;
    m_uiConfig.focusOnly = false;
    m_uiConfig.gameId = "110";
    m_uiConfig.enableInterval = true;
    m_uiConfig.windowWidth = 1200;
    m_uiConfig.windowHeight = 800;
    
    // 过滤配置默认值
    m_filterConfig.includeKeywords = "";
    m_filterConfig.excludeKeywords = "";
    m_filterConfig.minPrice = 0.0;
    m_filterConfig.maxPrice = 999999.0;
    m_filterConfig.enableAutoAccept = false;
    
    // 安全配置默认值
    m_securityConfig.enableTLSFingerprint = true;
    m_securityConfig.fingerprintType = "Quark";
    m_securityConfig.enableRandomDelay = false;
    m_securityConfig.minDelay = 100;
    m_securityConfig.maxDelay = 500;
}

void AppConfig::loadFromSettings()
{
    if (!m_settings) return;
    
    // 加载网络配置
    m_settings->beginGroup("Network");
    m_networkConfig.engineType = m_settings->value("engineType", m_networkConfig.engineType).toString();
    m_networkConfig.timeout = m_settings->value("timeout", m_networkConfig.timeout).toInt();
    m_networkConfig.refreshInterval = m_settings->value("refreshInterval", m_networkConfig.refreshInterval).toInt();
    m_networkConfig.enableDebugLog = m_settings->value("enableDebugLog", m_networkConfig.enableDebugLog).toBool();
    m_networkConfig.userAgent = m_settings->value("userAgent", m_networkConfig.userAgent).toString();
    m_settings->endGroup();
    
    // 加载UI配置
    m_settings->beginGroup("UI");
    m_uiConfig.pageSize = m_settings->value("pageSize", m_uiConfig.pageSize).toInt();
    m_uiConfig.focusOnly = m_settings->value("focusOnly", m_uiConfig.focusOnly).toBool();
    m_uiConfig.gameId = m_settings->value("gameId", m_uiConfig.gameId).toString();
    m_uiConfig.enableInterval = m_settings->value("enableInterval", m_uiConfig.enableInterval).toBool();
    m_uiConfig.windowWidth = m_settings->value("windowWidth", m_uiConfig.windowWidth).toInt();
    m_uiConfig.windowHeight = m_settings->value("windowHeight", m_uiConfig.windowHeight).toInt();
    m_settings->endGroup();
    
    // 加载过滤配置
    m_settings->beginGroup("Filter");
    m_filterConfig.includeKeywords = m_settings->value("includeKeywords", m_filterConfig.includeKeywords).toString();
    m_filterConfig.excludeKeywords = m_settings->value("excludeKeywords", m_filterConfig.excludeKeywords).toString();
    m_filterConfig.minPrice = m_settings->value("minPrice", m_filterConfig.minPrice).toDouble();
    m_filterConfig.maxPrice = m_settings->value("maxPrice", m_filterConfig.maxPrice).toDouble();
    m_filterConfig.enableAutoAccept = m_settings->value("enableAutoAccept", m_filterConfig.enableAutoAccept).toBool();
    m_settings->endGroup();
    
    // 加载安全配置
    m_settings->beginGroup("Security");
    m_securityConfig.enableTLSFingerprint = m_settings->value("enableTLSFingerprint", m_securityConfig.enableTLSFingerprint).toBool();
    m_securityConfig.fingerprintType = m_settings->value("fingerprintType", m_securityConfig.fingerprintType).toString();
    m_securityConfig.enableRandomDelay = m_settings->value("enableRandomDelay", m_securityConfig.enableRandomDelay).toBool();
    m_securityConfig.minDelay = m_settings->value("minDelay", m_securityConfig.minDelay).toInt();
    m_securityConfig.maxDelay = m_settings->value("maxDelay", m_securityConfig.maxDelay).toInt();
    m_settings->endGroup();
}

void AppConfig::saveToSettings()
{
    if (!m_settings) return;
    
    // 保存网络配置
    m_settings->beginGroup("Network");
    m_settings->setValue("engineType", m_networkConfig.engineType);
    m_settings->setValue("timeout", m_networkConfig.timeout);
    m_settings->setValue("refreshInterval", m_networkConfig.refreshInterval);
    m_settings->setValue("enableDebugLog", m_networkConfig.enableDebugLog);
    m_settings->setValue("userAgent", m_networkConfig.userAgent);
    m_settings->endGroup();
    
    // 保存UI配置
    m_settings->beginGroup("UI");
    m_settings->setValue("pageSize", m_uiConfig.pageSize);
    m_settings->setValue("focusOnly", m_uiConfig.focusOnly);
    m_settings->setValue("gameId", m_uiConfig.gameId);
    m_settings->setValue("enableInterval", m_uiConfig.enableInterval);
    m_settings->setValue("windowWidth", m_uiConfig.windowWidth);
    m_settings->setValue("windowHeight", m_uiConfig.windowHeight);
    m_settings->endGroup();
    
    // 保存过滤配置
    m_settings->beginGroup("Filter");
    m_settings->setValue("includeKeywords", m_filterConfig.includeKeywords);
    m_settings->setValue("excludeKeywords", m_filterConfig.excludeKeywords);
    m_settings->setValue("minPrice", m_filterConfig.minPrice);
    m_settings->setValue("maxPrice", m_filterConfig.maxPrice);
    m_settings->setValue("enableAutoAccept", m_filterConfig.enableAutoAccept);
    m_settings->endGroup();
    
    // 保存安全配置
    m_settings->beginGroup("Security");
    m_settings->setValue("enableTLSFingerprint", m_securityConfig.enableTLSFingerprint);
    m_settings->setValue("fingerprintType", m_securityConfig.fingerprintType);
    m_settings->setValue("enableRandomDelay", m_securityConfig.enableRandomDelay);
    m_settings->setValue("minDelay", m_securityConfig.minDelay);
    m_settings->setValue("maxDelay", m_securityConfig.maxDelay);
    m_settings->endGroup();
}

bool AppConfig::validate() const
{
    // 验证网络配置
    if (m_networkConfig.timeout < 1000 || m_networkConfig.timeout > 300000) {
        return false;
    }
    if (m_networkConfig.refreshInterval < 100 || m_networkConfig.refreshInterval > 60000) {
        return false;
    }
    
    // 验证UI配置
    if (m_uiConfig.pageSize < 1 || m_uiConfig.pageSize > 200) {
        return false;
    }
    
    // 验证过滤配置
    if (m_filterConfig.minPrice < 0 || m_filterConfig.maxPrice < m_filterConfig.minPrice) {
        return false;
    }
    
    // 验证安全配置
    if (m_securityConfig.minDelay < 0 || m_securityConfig.maxDelay < m_securityConfig.minDelay) {
        return false;
    }
    
    return true;
}

QStringList AppConfig::getValidationErrors() const
{
    QStringList errors;
    
    if (m_networkConfig.timeout < 1000 || m_networkConfig.timeout > 300000) {
        errors << "网络超时时间必须在1-300秒之间";
    }
    if (m_networkConfig.refreshInterval < 100 || m_networkConfig.refreshInterval > 60000) {
        errors << "刷新间隔必须在100-60000毫秒之间";
    }
    if (m_uiConfig.pageSize < 1 || m_uiConfig.pageSize > 200) {
        errors << "页面大小必须在1-200之间";
    }
    if (m_filterConfig.minPrice < 0 || m_filterConfig.maxPrice < m_filterConfig.minPrice) {
        errors << "价格范围设置无效";
    }
    if (m_securityConfig.minDelay < 0 || m_securityConfig.maxDelay < m_securityConfig.minDelay) {
        errors << "延迟范围设置无效";
    }
    
    return errors;
}
